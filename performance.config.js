// Performance configuration for RiftStays
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
})

// Performance monitoring configuration
const performanceConfig = {
  // Core Web Vitals thresholds
  webVitals: {
    LCP: 2500, // Largest Contentful Paint (ms)
    FID: 100,  // First Input Delay (ms)
    CLS: 0.1,  // Cumulative Layout Shift
    FCP: 1800, // First Contentful Paint (ms)
    TTFB: 600, // Time to First Byte (ms)
  },

  // Image optimization settings
  images: {
    quality: 85,
    formats: ['image/avif', 'image/webp'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // Bundle optimization
  bundle: {
    maxSize: {
      total: 500000, // 500KB
      chunks: 250000, // 250KB per chunk
    },
    splitChunks: {
      vendor: true,
      framework: true,
      commons: true,
    },
  },

  // Caching strategy
  cache: {
    static: 31536000, // 1 year for static assets
    api: 3600,        // 1 hour for API responses
    pages: 86400,     // 1 day for pages
  },

  // Preloading strategy
  preload: {
    fonts: ['/fonts/inter.woff2'],
    images: ['/hero-bg.jpg'],
    scripts: [],
  },

  // Compression settings
  compression: {
    gzip: true,
    brotli: true,
    level: 6,
  },
}

module.exports = {
  performanceConfig,
  withBundleAnalyzer,
}
