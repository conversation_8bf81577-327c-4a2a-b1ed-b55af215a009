import type { Metadata } from 'next'
import './globals.css'
import { Header } from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import { getSettings } from '@/sanity/lib/queries'
import { generateSEO } from '@/components/seo/SEO'
import { WebVitals } from '@/components/performance/WebVitals'
import { ServiceWorkerRegistration } from '@/components/ServiceWorkerRegistration'
import { Suspense } from 'react'

export async function generateMetadata(): Promise<Metadata> {
  let settings
  try {
    settings = await getSettings()
  } catch (error) {
    console.warn('Failed to fetch settings for metadata:', error)
    settings = null
  }
  return generateSEO({ settings })
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  let settings
  try {
    settings = await getSettings()
  } catch (error) {
    console.warn('Failed to fetch settings from Sanity:', error)
    settings = null
  }

  return (
    <html lang="en">
      <head>
        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://cdn.sanity.io" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* DNS prefetch for better performance */}
        <link rel="dns-prefetch" href="https://cdn.sanity.io" />

        {/* Viewport meta tag for responsive design */}
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />

        {/* Theme color for mobile browsers */}
        <meta name="theme-color" content="#2AAA8A" />
        <meta name="msapplication-TileColor" content="#2AAA8A" />

        {/* PWA manifest */}
        <link rel="manifest" href="/manifest.json" />

        {/* Apple touch icons */}
        <link rel="apple-touch-icon" href="/logo.svg" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="RiftStays" />

        {/* Preload critical resources */}
        <link
          rel="preload"
          href="/hero-bg.jpg"
          as="image"
          type="image/jpeg"
        />
        <link
          rel="preload"
          href="/logo.svg"
          as="image"
          type="image/svg+xml"
        />
        <link
          rel="preload"
          href="/logo-white.svg"
          as="image"
          type="image/svg+xml"
        />


      </head>
      <body className="antialiased">
        {/* Performance monitoring */}
        <WebVitals />

        {/* Service Worker Registration */}
        <ServiceWorkerRegistration />

        {/* Header with suspense boundary */}
        <Suspense fallback={<div className="h-16 bg-white" />}>
          <Header settings={settings} />
        </Suspense>

        {/* Main content */}
        <main className="min-h-screen">
          {children}
        </main>

        {/* Footer with suspense boundary */}
        <Suspense fallback={<div className="h-32 bg-gray-100" />}>
          <Footer settings={settings} />
        </Suspense>
      </body>
    </html>
  )
}
